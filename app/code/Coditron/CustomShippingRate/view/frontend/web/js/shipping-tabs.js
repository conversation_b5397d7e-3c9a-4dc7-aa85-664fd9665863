define([
    'jquery',
    'jquery/ui'
], function ($) {
    'use strict';

    $.widget('coditron.shippingTabs', {
        options: {
            tabSelector: '.wk-mp-tab-item',
            tabLinkSelector: '.wk-mp-tab-link',
            tabPanelSelector: '.wk-mp-tab-panel',
            activeClass: 'active'
        },

        _create: function () {
            this._bind();
            this._initActiveTab();
        },

        _bind: function () {
            var self = this;
            
            this.element.on('click', this.options.tabLinkSelector, function (e) {
                e.preventDefault();
                var $link = $(this);
                var $tabItem = $link.closest(self.options.tabSelector);
                var targetTab = $link.attr('href').substring(1);
                
                self._switchTab($tabItem, targetTab);
            });
        },

        _initActiveTab: function () {
            // Check URL hash for active tab
            var hash = window.location.hash.substring(1);
            if (hash && this.element.find('#' + hash).length) {
                var $targetTab = this.element.find('[data-tab="' + hash + '"]');
                if ($targetTab.length) {
                    this._switchTab($targetTab, hash);
                    return;
                }
            }
            
            // Default to first tab if no hash or invalid hash
            var $firstTab = this.element.find(this.options.tabSelector).first();
            var firstTabId = $firstTab.data('tab');
            this._switchTab($firstTab, firstTabId);
        },

        _switchTab: function ($tabItem, targetTab) {
            // Remove active class from all tabs and panels
            this.element.find(this.options.tabSelector).removeClass(this.options.activeClass);
            this.element.find(this.options.tabPanelSelector).removeClass(this.options.activeClass);
            
            // Add active class to selected tab and panel
            $tabItem.addClass(this.options.activeClass);
            this.element.find('#' + targetTab).addClass(this.options.activeClass);
            
            // Update URL hash without triggering page scroll
            if (history.pushState) {
                history.pushState(null, null, '#' + targetTab);
            } else {
                window.location.hash = targetTab;
            }
            
            // Trigger custom event for other components to listen to
            this.element.trigger('tabSwitch', [targetTab]);
        }
    });

    return $.coditron.shippingTabs;
});
