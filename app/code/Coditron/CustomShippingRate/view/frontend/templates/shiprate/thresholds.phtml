<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Coditron_CustomShippingRate
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
?>
<div class="wk-mp-thresholds-container">
    <div class="page-main-actions">
        <div class="page-actions-placeholder"></div>
        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
            <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
                <div class="page-actions-buttons">
                    <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
                    class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                    onclick="location.href
                    = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new', ['threshold' => 1]))?>';"
                    data-ui-id="add-threshold-button">
                        <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?= /* @noEscape */ $block->getChildHtml(); ?>
</div>
