<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="sales_order">
		<column name="shipping_data" nullable="true" xsi:type="varchar" comment="Shipping Data" length="1024"/>
	</table>
	<table name="quote">
		<column name="shipping_data" nullable="true" xsi:type="varchar" comment="Shipping Data" length="1024"/>
	</table>
	<table name="coditron_customshippingrate_shiptablerates" resource="default" engine="innodb" comment="coditron_customshippingrate_shiptablerates Table">
		<column xsi:type="int" name="shiptablerates_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="shiptablerates_id"/>
		</constraint>
		<column name="courier_name" nullable="true" xsi:type="varchar" comment="Courier name" length="255"/>
        <column name="service_type" nullable="false" xsi:type="varchar" comment="Service Type" length="255"/>
        <column name="countries" nullable="true" xsi:type="varchar" comment="Countries" length="1024"/>
        <column name="return_address_id" xsi:type="int" unsigned="true" nullable="true" identity="false" padding="10" default="0" comment="Return Address Id"/>
        <column name="packing_time" xsi:type="tinyint" unsigned="true" nullable="true" identity="false" padding="7" default="0" comment="Packing Time"/>
        <column name="delivery_time" xsi:type="tinyint" unsigned="true" nullable="true" identity="false" padding="7" default="0" comment="Delivery Time"/>
        <column name="total_lead_time" xsi:type="tinyint" unsigned="true" nullable="true" identity="false" padding="7" default="0" comment="Total Lead Time"/>
        <column name="weight" nullable="true" xsi:type="varchar" comment="Weight (Up to, in Kg)" length="255"/>
        <column name="shipping_price" nullable="true" xsi:type="varchar" comment="Shipping price" length="255"/>
        <column name="free_shipping" nullable="false" xsi:type="boolean" default="false" comment="Free Shipping"/>
        <column name="min_amount" xsi:type="decimal" precision="12" scale="2" nullable="true" default="0.00" comment="Minimum Amount for Free Shipping"/>
		<column name="seller_id" xsi:type="int" unsigned="true" nullable="false" identity="false" padding="10" default="0" comment="Seller Id"/>
		<constraint xsi:type="foreign" referenceId="FK_CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" table="coditron_customshippingrate_shiptablerates" column="seller_id" referenceTable="customer_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
        <index referenceId="CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" indexType="btree">
			<column name="seller_id"/>
		</index>
	</table>
</schema>
