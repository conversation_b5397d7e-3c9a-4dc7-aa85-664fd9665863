<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Co<PERSON>ron\CustomShippingRate\Ui\DataProvider;

use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
use Magento\Customer\Model\Session;

class ThresholdListDataProvider extends AbstractDataProvider
{
    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @inheritDoc
     */
    protected $collection;

    /**
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param DataPersistorInterface $dataPersistor
     * @param MarketplaceHelper $marketplaceHelper
     * @param Session $customerSession
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        DataPersistorInterface $dataPersistor,
        MarketplaceHelper $marketplaceHelper,
        Session $customerSession,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        $this->marketplaceHelper = $marketplaceHelper;
        $this->customerSession = $customerSession;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->prepareCollection();
    }

    /**
     * Prepare collection for thresholds only
     */
    protected function prepareCollection()
    {
        $sellerId = $this->marketplaceHelper->getCustomerId();

        // Filter for current seller
        $this->collection->addFieldToFilter('seller_id', $sellerId);

        // For now, show empty collection until min_amount field is added to database
        // This will prevent loading errors
        $this->collection->addFieldToFilter('shiptablerates_id', ['eq' => -1]);
    }

    /**
     * @inheritDoc
     */
    public function getData()
    {
        // Return empty data structure for now to prevent loading issues
        return [
            'totalRecords' => 0,
            'items' => []
        ];
    }
}
