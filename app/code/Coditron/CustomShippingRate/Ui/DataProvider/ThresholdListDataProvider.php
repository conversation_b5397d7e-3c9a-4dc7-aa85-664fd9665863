<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Ui\DataProvider;

use Coditron\CustomShippingRate\Ui\DataProvider\ShippingRateListDataProvider;

class ThresholdListDataProvider extends ShippingRateListDataProvider
{
    /**
     * Override to filter for thresholds only
     */
    public function getData()
    {
        $data = parent::getData();

        // Filter data to show only thresholds (records with min_amount > 0)
        if (isset($data['items'])) {
            $filteredItems = [];
            foreach ($data['items'] as $item) {
                // Check if this is a threshold record (min_amount is not null and > 0)
                if (isset($item['min_amount']) && $item['min_amount'] !== null && (float)$item['min_amount'] > 0) {
                    // Format min_amount for display
                    $item['min_amount'] = number_format((float)$item['min_amount'], 2);

                    // Handle NULL countries - show as "Not specified" or empty
                    if (!isset($item['countries']) || $item['countries'] === null || $item['countries'] === '') {
                        $item['countries'] = __('All Countries');
                    } else {
                        // Format countries for display (replace commas with comma-space)
                        $item['countries'] = str_replace(',', ', ', $item['countries']);
                    }

                    $filteredItems[] = $item;
                }
            }
            $data['items'] = $filteredItems;
            $data['totalRecords'] = count($filteredItems);
        }

        return $data;
    }
}
