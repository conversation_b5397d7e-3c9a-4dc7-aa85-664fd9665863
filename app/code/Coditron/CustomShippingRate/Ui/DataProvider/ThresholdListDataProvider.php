<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Co<PERSON>ron\CustomShippingRate\Ui\DataProvider;

use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
use Magento\Customer\Model\Session;

class ThresholdListDataProvider extends AbstractDataProvider
{
    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @inheritDoc
     */
    protected $collection;

    /**
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param DataPersistorInterface $dataPersistor
     * @param MarketplaceHelper $marketplaceHelper
     * @param Session $customerSession
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        DataPersistorInterface $dataPersistor,
        MarketplaceHelper $marketplaceHelper,
        Session $customerSession,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        $this->marketplaceHelper = $marketplaceHelper;
        $this->customerSession = $customerSession;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->prepareCollection();
    }

    /**
     * Prepare collection for thresholds only
     */
    protected function prepareCollection()
    {
        $sellerId = $this->marketplaceHelper->getCustomerId();
        
        // Filter for current seller and only records with min_amount > 0 (thresholds)
        $this->collection->addFieldToFilter('seller_id', $sellerId)
                        ->addFieldToFilter('min_amount', ['gt' => 0]);
    }

    /**
     * @inheritDoc
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }
        
        $items = $this->collection->getItems();
        foreach ($items as $model) {
            $data = $model->getData();
            // Format countries for display
            if (isset($data['countries'])) {
                $data['countries'] = str_replace(',', ', ', $data['countries']);
            }
            $this->loadedData[$model->getId()] = $data;
        }
        
        $data = $this->dataPersistor->get('coditron_customshippingrate_shiptablerates');
        
        if (!empty($data)) {
            $model = $this->collection->getNewEmptyItem();
            $model->setData($data);
            $this->loadedData[$model->getId()] = $model->getData();
            $this->dataPersistor->clear('coditron_customshippingrate_shiptablerates');
        }
        
        return $this->loadedData;
    }
}
