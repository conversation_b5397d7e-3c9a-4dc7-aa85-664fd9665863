<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Co<PERSON>ron\CustomShippingRate\Ui\DataProvider;

use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
use Magento\Customer\Model\Session;

class ThresholdListDataProvider extends AbstractDataProvider
{
    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @inheritDoc
     */
    protected $collection;

    /**
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param DataPersistorInterface $dataPersistor
     * @param MarketplaceHelper $marketplaceHelper
     * @param Session $customerSession
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        DataPersistorInterface $dataPersistor,
        MarketplaceHelper $marketplaceHelper,
        Session $customerSession,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->dataPersistor = $dataPersistor;
        $this->marketplaceHelper = $marketplaceHelper;
        $this->customerSession = $customerSession;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->prepareCollection();
    }

    /**
     * Prepare collection for thresholds only
     */
    protected function prepareCollection()
    {
        $sellerId = $this->marketplaceHelper->getCustomerId();

        // Filter for current seller
        $this->collection->addFieldToFilter('seller_id', $sellerId);

        try {
            // Filter for thresholds - records where min_amount > 0
            $this->collection->addFieldToFilter('min_amount', ['gt' => 0]);
        } catch (\Exception $e) {
            // If min_amount field doesn't exist yet, show empty collection
            $this->collection->addFieldToFilter('shiptablerates_id', ['eq' => -1]);
        }
    }

    /**
     * @inheritDoc
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }

        try {
            $items = $this->collection->getItems();
            foreach ($items as $model) {
                $data = $model->getData();
                // Format countries for display
                if (isset($data['countries'])) {
                    $data['countries'] = str_replace(',', ', ', $data['countries']);
                }
                // Format min_amount for display
                if (isset($data['min_amount'])) {
                    $data['min_amount'] = number_format((float)$data['min_amount'], 2);
                }
                $this->loadedData[$model->getId()] = $data;
            }

            $data = $this->dataPersistor->get('coditron_customshippingrate_shiptablerates');

            if (!empty($data)) {
                $model = $this->collection->getNewEmptyItem();
                $model->setData($data);
                $this->loadedData[$model->getId()] = $model->getData();
                $this->dataPersistor->clear('coditron_customshippingrate_shiptablerates');
            }
        } catch (\Exception $e) {
            // If there's an error (e.g., min_amount field doesn't exist), return empty data
            $this->loadedData = [];
        }

        return $this->loadedData ?: [];
    }
}
